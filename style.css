* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: radial-gradient(circle at center, #0a0a0a 0%, #000000 100%);
    height: 100vh;
    overflow: hidden;
    font-family: Arial, sans-serif;
}

.container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    perspective: 1000px;
}

.plasma-sphere {
    position: relative;
    width: 400px;
    height: 400px;
    transform-style: preserve-3d;
    animation: rotate3d 20s linear infinite;
}

@keyframes rotate3d {
    0% { transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg); }
    100% { transform: rotateX(360deg) rotateY(360deg) rotateZ(360deg); }
}

.sphere-core {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300px;
    height: 300px;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%, 
        rgba(255, 255, 255, 0.8) 0%,
        rgba(138, 43, 226, 0.6) 20%,
        rgba(75, 0, 130, 0.4) 40%,
        rgba(25, 25, 112, 0.3) 60%,
        rgba(0, 0, 0, 0.8) 100%);
    box-shadow: 
        0 0 50px rgba(138, 43, 226, 0.8),
        0 0 100px rgba(75, 0, 130, 0.6),
        inset 0 0 50px rgba(255, 255, 255, 0.2);
    animation: pulse 3s ease-in-out infinite alternate;
}

@keyframes pulse {
    0% { 
        box-shadow: 
            0 0 50px rgba(138, 43, 226, 0.8),
            0 0 100px rgba(75, 0, 130, 0.6),
            inset 0 0 50px rgba(255, 255, 255, 0.2);
    }
    100% { 
        box-shadow: 
            0 0 80px rgba(138, 43, 226, 1),
            0 0 150px rgba(75, 0, 130, 0.8),
            inset 0 0 80px rgba(255, 255, 255, 0.4);
    }
}

.plasma-layer {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 320px;
    height: 320px;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    background: radial-gradient(circle at center,
        transparent 40%,
        rgba(0, 255, 255, 0.1) 50%,
        rgba(255, 0, 255, 0.1) 70%,
        transparent 100%);
    animation: plasmaFlow 4s ease-in-out infinite;
}

@keyframes plasmaFlow {
    0%, 100% { 
        background: radial-gradient(circle at center,
            transparent 40%,
            rgba(0, 255, 255, 0.1) 50%,
            rgba(255, 0, 255, 0.1) 70%,
            transparent 100%);
    }
    50% { 
        background: radial-gradient(circle at center,
            transparent 40%,
            rgba(255, 0, 255, 0.2) 50%,
            rgba(0, 255, 255, 0.2) 70%,
            transparent 100%);
    }
}

.spark {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2px;
    height: 150px;
    background: linear-gradient(to bottom,
        rgba(255, 255, 255, 1) 0%,
        rgba(0, 255, 255, 0.8) 30%,
        rgba(255, 0, 255, 0.6) 60%,
        transparent 100%);
    transform-origin: bottom center;
    border-radius: 1px;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
    animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% { 
        opacity: 0.3;
        transform: translate(-50%, -100%) scaleY(0.5);
    }
    50% { 
        opacity: 1;
        transform: translate(-50%, -100%) scaleY(1.2);
    }
}

/* Individuelle Spark-Positionen */
.spark-1 { transform: translate(-50%, -100%) rotate(0deg); animation-delay: 0s; }
.spark-2 { transform: translate(-50%, -100%) rotate(30deg); animation-delay: 0.2s; }
.spark-3 { transform: translate(-50%, -100%) rotate(60deg); animation-delay: 0.4s; }
.spark-4 { transform: translate(-50%, -100%) rotate(90deg); animation-delay: 0.6s; }
.spark-5 { transform: translate(-50%, -100%) rotate(120deg); animation-delay: 0.8s; }
.spark-6 { transform: translate(-50%, -100%) rotate(150deg); animation-delay: 1s; }
.spark-7 { transform: translate(-50%, -100%) rotate(180deg); animation-delay: 1.2s; }
.spark-8 { transform: translate(-50%, -100%) rotate(210deg); animation-delay: 1.4s; }
.spark-9 { transform: translate(-50%, -100%) rotate(240deg); animation-delay: 1.6s; }
.spark-10 { transform: translate(-50%, -100%) rotate(270deg); animation-delay: 1.8s; }
.spark-11 { transform: translate(-50%, -100%) rotate(300deg); animation-delay: 0.1s; }
.spark-12 { transform: translate(-50%, -100%) rotate(330deg); animation-delay: 0.3s; }

.plasma-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    border-radius: 50%;
    border: 2px solid transparent;
    animation: ringPulse 3s ease-in-out infinite;
}

.ring-1 {
    width: 350px;
    height: 350px;
    transform: translate(-50%, -50%);
    border-color: rgba(0, 255, 255, 0.3);
    animation-delay: 0s;
}

.ring-2 {
    width: 380px;
    height: 380px;
    transform: translate(-50%, -50%);
    border-color: rgba(255, 0, 255, 0.2);
    animation-delay: 1s;
}

.ring-3 {
    width: 410px;
    height: 410px;
    transform: translate(-50%, -50%);
    border-color: rgba(255, 255, 0, 0.1);
    animation-delay: 2s;
}

@keyframes ringPulse {
    0%, 100% {
        opacity: 0.2;
        transform: translate(-50%, -50%) scale(0.8);
    }
    50% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1.1);
    }
}

.electric-arc {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 2px;
    background: linear-gradient(to right,
        transparent 0%,
        rgba(255, 255, 255, 0.8) 20%,
        rgba(0, 255, 255, 1) 50%,
        rgba(255, 255, 255, 0.8) 80%,
        transparent 100%);
    transform-origin: left center;
    border-radius: 1px;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.8);
    animation: arcFlicker 1.5s ease-in-out infinite;
}

@keyframes arcFlicker {
    0%, 100% {
        opacity: 0.3;
        transform: translate(-50%, -50%) scaleX(0.5);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scaleX(1.2);
    }
}

.arc-1 {
    transform: translate(-50%, -50%) rotate(45deg);
    animation-delay: 0s;
}

.arc-2 {
    transform: translate(-50%, -50%) rotate(135deg);
    animation-delay: 0.4s;
}

.arc-3 {
    transform: translate(-50%, -50%) rotate(225deg);
    animation-delay: 0.8s;
}

.arc-4 {
    transform: translate(-50%, -50%) rotate(315deg);
    animation-delay: 1.2s;
}

.glow-effect {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 600px;
    height: 600px;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    background: radial-gradient(circle at center,
        transparent 0%,
        rgba(138, 43, 226, 0.1) 40%,
        rgba(75, 0, 130, 0.05) 70%,
        transparent 100%);
    animation: glowPulse 5s ease-in-out infinite;
    pointer-events: none;
}

@keyframes glowPulse {
    0%, 100% {
        opacity: 0.5;
        transform: translate(-50%, -50%) scale(0.8);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.2);
    }
}
