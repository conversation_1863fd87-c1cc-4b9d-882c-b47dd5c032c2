// 3D Plasma Kugel JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const plasmaSphere = document.querySelector('.plasma-sphere');
    const sparks = document.querySelectorAll('.spark');
    const electricArcs = document.querySelectorAll('.electric-arc');
    const plasmaRings = document.querySelectorAll('.plasma-ring');
    
    // Dynamische Farb-Animation für Funken
    function animateSparks() {
        sparks.forEach((spark, index) => {
            const colors = [
                'linear-gradient(to bottom, rgba(255,255,255,1) 0%, rgba(0,255,255,0.8) 30%, rgba(255,0,255,0.6) 60%, transparent 100%)',
                'linear-gradient(to bottom, rgba(255,255,255,1) 0%, rgba(255,0,255,0.8) 30%, rgba(0,255,0,0.6) 60%, transparent 100%)',
                'linear-gradient(to bottom, rgba(255,255,255,1) 0%, rgba(255,255,0,0.8) 30%, rgba(255,0,0,0.6) 60%, transparent 100%)',
                'linear-gradient(to bottom, rgba(255,255,255,1) 0%, rgba(0,255,0,0.8) 30%, rgba(0,0,255,0.6) 60%, transparent 100%)'
            ];
            
            setInterval(() => {
                const randomColor = colors[Math.floor(Math.random() * colors.length)];
                spark.style.background = randomColor;
            }, 1000 + (index * 200));
        });
    }
    
    // Dynamische Intensität für elektrische Bögen
    function animateElectricArcs() {
        electricArcs.forEach((arc, index) => {
            setInterval(() => {
                const intensity = Math.random() * 0.5 + 0.5;
                const hue = Math.random() * 60 + 180; // Cyan bis Blau
                
                arc.style.background = `linear-gradient(to right,
                    transparent 0%,
                    hsla(${hue}, 100%, 80%, ${intensity * 0.8}) 20%,
                    hsla(${hue}, 100%, 90%, ${intensity}) 50%,
                    hsla(${hue}, 100%, 80%, ${intensity * 0.8}) 80%,
                    transparent 100%)`;
                    
                arc.style.boxShadow = `0 0 ${15 * intensity}px hsla(${hue}, 100%, 70%, ${intensity})`;
            }, 500 + (index * 300));
        });
    }
    
    // Plasma-Ring Farb-Animation
    function animatePlasmaRings() {
        plasmaRings.forEach((ring, index) => {
            setInterval(() => {
                const hue = (Date.now() * 0.1 + index * 120) % 360;
                const opacity = Math.sin(Date.now() * 0.003 + index) * 0.3 + 0.5;
                
                ring.style.borderColor = `hsla(${hue}, 100%, 60%, ${opacity})`;
            }, 50);
        });
    }
    
    // Maus-Interaktion
    function addMouseInteraction() {
        let mouseX = 0;
        let mouseY = 0;
        
        document.addEventListener('mousemove', (e) => {
            mouseX = (e.clientX / window.innerWidth - 0.5) * 2;
            mouseY = (e.clientY / window.innerHeight - 0.5) * 2;
            
            // Leichte Rotation basierend auf Mausposition
            const rotationX = mouseY * 10;
            const rotationY = mouseX * 10;
            
            plasmaSphere.style.transform = `rotateX(${rotationX}deg) rotateY(${rotationY}deg)`;
        });
        
        // Klick-Effekt für Energie-Boost
        document.addEventListener('click', () => {
            plasmaSphere.style.animation = 'none';
            plasmaSphere.style.transform = 'scale(1.2)';
            
            setTimeout(() => {
                plasmaSphere.style.animation = 'rotate3d 20s linear infinite';
                plasmaSphere.style.transform = 'scale(1)';
            }, 200);
        });
    }
    
    // Zufällige Plasma-Entladungen
    function createRandomDischarges() {
        setInterval(() => {
            const randomSpark = sparks[Math.floor(Math.random() * sparks.length)];
            randomSpark.style.animation = 'none';
            randomSpark.style.transform += ' scaleY(2)';
            randomSpark.style.opacity = '1';
            
            setTimeout(() => {
                randomSpark.style.animation = 'sparkle 2s ease-in-out infinite';
                randomSpark.style.transform = randomSpark.style.transform.replace(' scaleY(2)', '');
            }, 300);
        }, 2000);
    }
    
    // Kern-Pulsation verstärken
    function enhanceCorePulsation() {
        const sphereCore = document.querySelector('.sphere-core');
        
        setInterval(() => {
            const intensity = Math.random() * 0.5 + 0.5;
            const scale = 0.95 + (intensity * 0.1);
            
            sphereCore.style.transform = `translate(-50%, -50%) scale(${scale})`;
            
            setTimeout(() => {
                sphereCore.style.transform = 'translate(-50%, -50%) scale(1)';
            }, 100);
        }, 1500);
    }
    
    // Initialisierung aller Animationen
    animateSparks();
    animateElectricArcs();
    animatePlasmaRings();
    addMouseInteraction();
    createRandomDischarges();
    enhanceCorePulsation();
    
    // Performance-Optimierung
    let animationFrame;
    function optimizeAnimations() {
        if (document.hidden) {
            cancelAnimationFrame(animationFrame);
        } else {
            animationFrame = requestAnimationFrame(optimizeAnimations);
        }
    }
    
    document.addEventListener('visibilitychange', optimizeAnimations);
    optimizeAnimations();
});

// Zusätzliche Utility-Funktionen
function getRandomColor() {
    const colors = ['#00ffff', '#ff00ff', '#ffff00', '#ff0080', '#80ff00', '#0080ff'];
    return colors[Math.floor(Math.random() * colors.length)];
}

function createParticleEffect(x, y) {
    const particle = document.createElement('div');
    particle.style.position = 'absolute';
    particle.style.left = x + 'px';
    particle.style.top = y + 'px';
    particle.style.width = '4px';
    particle.style.height = '4px';
    particle.style.backgroundColor = getRandomColor();
    particle.style.borderRadius = '50%';
    particle.style.pointerEvents = 'none';
    particle.style.animation = 'particleFade 1s ease-out forwards';
    
    document.body.appendChild(particle);
    
    setTimeout(() => {
        document.body.removeChild(particle);
    }, 1000);
}

// CSS für Partikel-Animation hinzufügen
const style = document.createElement('style');
style.textContent = `
    @keyframes particleFade {
        0% {
            opacity: 1;
            transform: scale(1);
        }
        100% {
            opacity: 0;
            transform: scale(0) translateY(-50px);
        }
    }
`;
document.head.appendChild(style);
